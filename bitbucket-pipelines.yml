image: atlassian/default-image:3

pipelines:
  branches:
    develop:
      - step:
          name: Deploy to develop
          deployment: develop
          script:
            - pipe: atlassian/ssh-run:0.4.0
              variables:
                SSH_USER: $NOSO_SERVER_USER
                SERVER: $NOSO_SERVER_IP
                COMMAND: |
                  ssh-keyscan -H $NOSO_SERVER_IP >> ~/.ssh/known_hosts
                  set -e
                  cd /var/www/kera-dev/
                  git pull origin develop
                  rm -f composer.lock
                  composer install
                  rm -rf bootstrap/cache/*
                  composer dump-autoload
                  php artisan migrate
                  php artisan db:seed
                  export NVM_DIR="/home/<USER>/.nvm"
                  [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
                  [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
                  source /home/<USER>/.nvm/nvm.sh
                  nvm use 22.15.0
                  npm install 
                  npm run build
    testing:
      - step:
          name: Deploy to testing
          deployment: testing
          script:
            - pipe: atlassian/ssh-run:0.4.0
              variables:
                SSH_USER: $NOSO_SERVER_USER
                SERVER: $NOSO_SERVER_IP
                COMMAND: |
                  ssh-keyscan -H $NOSO_SERVER_IP >> ~/.ssh/known_hosts
                  set -e
                  cd /var/www/kera-test/
                  git pull origin testing
                  rm -f composer.lock
                  composer install
                  rm -rf bootstrap/cache/*
                  composer dump-autoload
                  php artisan migrate
                  php artisan db:seed
                  export NVM_DIR="/home/<USER>/.nvm"
                  [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
                  [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
                  source /home/<USER>/.nvm/nvm.sh
                  nvm use 22.15.0
                  npm install 
                  npm run build
    pre-production:
      - step:
          name: Deploy to pre-production
          deployment: pre-production
          script:
            - pipe: atlassian/ssh-run:0.4.0
              variables:
                SSH_USER: $SERVER_USER_SA
                SERVER: $SERVER_IP_SA
                COMMAND: |
                  ssh-keyscan -H $SERVER_IP_SA >> ~/.ssh/known_hosts
                  set -e
                  cd /var/www/dev.kera/
                  git pull origin pre-production
                  rm -f composer.lock
                  composer install --dry-run
                  composer install --no-scripts
                  rm -rf bootstrap/cache/*
                  composer dump-autoload
                  php artisan migrate --pretend
                  php artisan migrate
                  php artisan db:seed
                  export NVM_DIR="/home/<USER>/.nvm"
                  [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
                  [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
                  source /home/<USER>/.nvm/nvm.sh
                  nvm use v20.18.0
                  npm install 
                  npm run build
