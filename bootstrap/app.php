<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
//        $middleware->append( \Modules\Tenancy\app\Http\Middleware\ScopeTenant::class);
        $middleware->appendToGroup('web',\Modules\Tenancy\app\Http\Middleware\ScopeTenant::class);
        $middleware->appendToGroup('web',\Modules\Subscription\app\Http\Middleware\SubscriptionForAjax::class);

        $middleware->appendToGroup('api',\App\Http\Middleware\SetApiLocale::class);
        $middleware->alias([
            'auth.optional' => \App\Http\Middleware\OptionalSanctumAuth::class,
            'set.locale' => \App\Http\Middleware\SetApiLocale::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->withCommands([
        \Modules\Subscription\app\Console\ExpirationCommand::class
    ])
    ->create();
