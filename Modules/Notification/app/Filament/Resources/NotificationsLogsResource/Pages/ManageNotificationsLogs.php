<?php

namespace Modules\Notification\app\Filament\Resources\NotificationsLogsResource\Pages;

use Filament\Pages\Actions\Action;
use Filament\Resources\Pages\ManageRecords;
use TomatoPHP\FilamentAlerts\Models\NotificationsTemplate;
use Modules\Notification\app\Filament\Resources\NotificationsLogsResource;
use Filament\Actions;
use Modules\Notification\app\Filament\Resources\UserNotificationResource;

class ManageNotificationsLogs extends ManageRecords
{
    protected static string $resource = NotificationsLogsResource::class;


    protected function getHeaderActions(): array
    {
        return [
            Action::make(__('Notifications'))
                ->action(fn()=> redirect()->to(UserNotificationResource::getUrl('index')))
                ->color('danger'),
        ];
    }
}
