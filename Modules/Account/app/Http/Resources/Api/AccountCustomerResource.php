<?php

namespace Modules\Account\app\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class AccountCustomerResource extends JsonResource
{

    public function toArray($request)
    {
        return [
            "id" => $this->id ?? "",
            "name" => $this->name ?? null,
            "first_name" => $this->first_name ?? null,
            "second_name" => $this->second_name ?? null,
            "third_name" => $this->third_name ?? null,
            "last_name" => $this->last_name ?? null,
            "email" => $this->email ?? null,
            "phone" => $this->phone ?? null,
            "lang" => $this->lang ?? 'en',
            "otp" => (app()->environment(['production'])) ? "" : (string)$this->otp_code,
            "image" => $this->getMedia('profile')->first() ? $this->getMedia('profile')->first()->getUrl() : null,
            "id_type" => $this->id_type,
            "country_of_issue" => $this->country_of_issue,
            "national_id" => $this->national_id,
            "birth_date" => $this->birth_date,
            "account_roles" => $this->whenLoaded('accountRoles', function () {
                return AccountRoleResource::collection($this->accountRoles);
            })
        ];
    }
}
