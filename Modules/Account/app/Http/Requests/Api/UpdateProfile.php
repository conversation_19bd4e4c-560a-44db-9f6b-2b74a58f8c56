<?php

namespace Modules\Account\app\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProfile extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "image"=>"sometimes|mimes:jpeg,jpg,png,gif|max:10000",
            "first_name" => "sometimes|string|max:200",
            "second_name" => "sometimes|string|max:200",
            "third_name" => "sometimes|string|max:200",
            "last_name" => "sometimes|string|max:200",
            "birth_date" => "sometimes|date",
            "email" => "sometimes|email|unique:accounts,email,".auth()->user()->id,
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'image.mimes' => __('The image must be a file of type: jpeg, jpg, png, gif.'),
            'image.max' => __('The image may not be greater than 10MB.'),
            'first_name.string' => __('The first name must be a string.'),
            'first_name.max' => __('The first name may not be greater than 200 characters.'),
            'second_name.string' => __('The second name must be a string.'),
            'second_name.max' => __('The second name may not be greater than 200 characters.'),
            'third_name.string' => __('The third name must be a string.'),
            'third_name.max' => __('The third name may not be greater than 200 characters.'),
            'last_name.string' => __('The last name must be a string.'),
            'last_name.max' => __('The last name may not be greater than 200 characters.'),
            'birth_date.date' => __('The birth date must be a valid date.'),
            'email.email' => __('The email must be a valid email address.'),
            'email.unique' => __('This email is already taken.')
        ];
    }

}
