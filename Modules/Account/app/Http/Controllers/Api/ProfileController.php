<?php


namespace Modules\Account\app\Http\Controllers\Api;


use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Khaleds\Shared\Helpers\ApiResponse;
use Modules\Account\app\Http\Requests\Api\ChangePhone;
use Modules\Account\app\Http\Requests\Api\CheckPassword;
use Modules\Account\app\Http\Requests\Api\CloseAccount;
use Modules\Account\app\Http\Requests\Api\UpdateProfile;
use Modules\Account\app\Http\Resources\Api\AccountCustomerResource;
use Modules\Account\Services\ProfileService;

//use Modules\Notification\Services\NotificationService; todo handle notification module

class ProfileController extends Controller
{

    public function __construct(private ProfileService $profileService
//        , public NotificationService $notificationService
    )
    {
    }

    public function index()
    {

        return ApiResponse::data(new AccountCustomerResource(auth()->user()->load('accountRoles')), __('Data Retrieved Successfully'));

    }

    public function updateProfile(UpdateProfile $request)
    {

        $this->profileService->model = auth()->user();
        $result = $this->profileService->updateProfile($request->validated(), 'profile');

        if ($result)
            return ApiResponse::data(new AccountCustomerResource($this->profileService->model->load('accountRoles')), __('Data Retrieved Successfully'));

        return ApiResponse::errors($this->profileService->message);
    }

    public function changePhone(ChangePhone $request)
    {

        $this->profileService->model = auth()->user();

        if ($request->phone == auth()->user()->phone) {
            $result = $this->profileService->updatePhone($request->validated());

            if ($result) {
                //todo handle notification service
//                $this->notificationService->delete(auth()->user()->id);
                auth()->user()->currentAccessToken()->delete();
                return ApiResponse::data([
                    "otp" => $this->profileService->model->otp_code
                ], __('Data updated successfully'));
            }
        }
        return ApiResponse::errors(__('Phone Or Password is Incorrect'));

    }

    public function checkPassword(CheckPassword $request)
    {
        if (Auth::guard('accounts')->attempt([
            'password' => $request->password,
            'phone' => auth()->user()->phone
        ])) {

            return ApiResponse::success(__('Done !'));

        }
        return ApiResponse::errors(__('Something Went Wrong'));

    }

    public function deleteAccount(CloseAccount $request)
    {
        $user = $request->user();
        $user->is_active = 0;
        $user->closed_reason = $request->closed_reason ?? null;
        $user->save();
        auth()->user()->currentAccessToken()->delete();

        //todo handle notification service
        //$this->notificationService->delete($request->user()->id);

        return response()->json([
            'status' => 200,
            'message' => __('deleted .')
        ]);
    }


}
