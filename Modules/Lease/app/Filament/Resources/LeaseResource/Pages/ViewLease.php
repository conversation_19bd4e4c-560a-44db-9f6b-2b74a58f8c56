<?php

namespace Modules\Lease\app\Filament\Resources\LeaseResource\Pages;

use Filament\Actions;
use Filament\Forms\Form;
use Modules\Lease\Enums\LeaseEnum;
use Modules\Lease\app\Models\Lease;
use Filament\Forms\Components\Wizard;
use Filament\Resources\Pages\ViewRecord;
use Modules\Lease\app\Filament\Resources\LeaseResource;
use Illuminate\Support\Str;
use Filament\Notifications\Notification;
use Filament\Support\Enums\IconPosition;
use Modules\Invoice\Enums\InvoiceTypeEnum;
use Modules\Invoice\Enums\InvoiceStatusEnum;
use Modules\Account\app\Models\Account;
use Modules\Invoice\app\Models\Invoice;
use App\Events\LeasePublished;
use Filament\Actions\ActionGroup;
use App\Models\User;
use Modules\Invoice\Enums\InvoiceItemTypeEnum;
use Modules\Lease\app\Filament\Actions\EjarSyncAction;
use Modules\Lease\Enums\LeaseAutoRenewalEnum;
use Modules\Lease\Services\LeaseTermsFormatter;
use Modules\Property\Enums\EjarSyncStatus;
use Modules\Lease\app\Filament\Resources\LeaseResource\Actions\PublishLeaseAction;


class ViewLease extends ViewRecord
{
    protected static string $resource = LeaseResource::class;

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $TermsFormatter = new LeaseTermsFormatter();
        return array_merge(
            $data,
            $TermsFormatter->refillTerms($this->record->lease_type, $this->record->terms)
        );
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(
                $this->modifyFormSchema(
                    LeaseResource::form($form)->getComponents()
                )
            );
    }

    protected function modifyFormSchema(array $schema): array
    {
        return collect($schema)->map(function ($field) {
            if ($field instanceof Wizard) {
                return $field->submitAction(null)->skippable();
            }
            return $field;
        })->toArray();
    }


    protected function getHeaderActions(): array
    {
        return [
            ActionGroup::make([
                Actions\Action::make(__('terminate'))
                    ->color('danger')
                    ->icon('heroicon-o-x-circle')
                    ->visible(fn (Lease $record): bool => in_array($record->status, [LeaseEnum::PUBLISHED, LeaseEnum::NEAR_EXPIRE , LeaseEnum::Renewed]) && $record->auto_renewal !== LeaseAutoRenewalEnum::PENDING)
                    ->url(fn (Lease $record): string => url('admin/leases/'.$record->id.'/terminate'),true),
                Actions\Action::make(__('close'))
                    ->color('danger')
                    ->icon('heroicon-o-x-circle')
                    ->visible(fn (Lease $record): bool => $record->status === LeaseEnum::ENDED)
                    ->url(fn (Lease $record): string => url('admin/leases/'.$record->id.'/close'),true),
            ])->button()
            ->label(__('Commends'))
            ->icon('heroicon-m-chevron-down')
            ->iconPosition(IconPosition::After),
            Actions\EditAction::make()
                ->visible(fn (Lease $record): bool => $record->status === LeaseEnum::DRAFT),
            Actions\DeleteAction::make()
                ->visible(fn (Lease $record): bool => $record->status === LeaseEnum::DRAFT),
                //TODO add new status will allow syncying
            EjarSyncAction::make()
                 ->visible(fn (Lease $record): bool => $record->status === LeaseEnum::DRAFT)
                 ->disabled(fn (Lease $record): bool => $record->ejar_sync_status->value === EjarSyncStatus::SYNCED->value),
            PublishLeaseAction::make(),
        ];
    }
}
