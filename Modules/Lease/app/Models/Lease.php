<?php

namespace Modules\Lease\app\Models;

use App\Models\Document;
use Illuminate\Database\Eloquent\Builder;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;
use Khaleds\Shared\Models\BaseModel;
use Modules\Account\Enums\AccountRolesEnum;
use Modules\Account\Helpers\AccountHelper;
use Modules\BankAccount\app\Models\BankAccount;
use Modules\Company\app\Models\Company;
use Modules\Invoice\app\Models\Invoice;
use Modules\Invoice\app\Models\InvoiceItem;
use Modules\Property\app\Models\Property;
use Modules\Tenancy\Traits\BelongsToTenancy;
use Modules\Lease\Enums\LeaseMemberTypesEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Property\Enums\EjarSyncStatus;
use Modules\Request\app\Models\Request;
use Modules\Service\app\Models\Service;
use Modules\Lease\Enums\SubLeaseTypeEnum;

class Lease extends BaseModel
{
    use BelongsToTenancy;

    public $translatable = [];
    protected $guarded = ['id'];
    public $unitsToProcess = [];

    protected $casts = [
        'ejar_sync_status' => EjarSyncStatus::class,
        'last_synced_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('leaseScope', function (Builder $builder) {
            if (request()->is('api/*')) {
                $account = auth()->user();
                if ($account) {
                    $current_role = AccountHelper::CurrentRole();
                    if ($current_role) {
                        //todo refactor this condition
                        if ($current_role == AccountRolesEnum::OWNER) {
                            $current_role = LeaseMemberTypesEnum::LESSOR;
                        } elseif ($current_role == AccountRolesEnum::OWNER_REPRESENTER) {
                            $current_role = LeaseMemberTypesEnum::LESSOR_REPRESENTER;
                        }

                        $builder->whereHas('leaseMembers', function ($query) use ($account, $current_role) {
                            $query->where('member_id', $account->id)
                                ->where('member_role', $current_role);
                        });
                    } else {
                        $builder->whereRaw('1 = 0'); //return empty list
                    }
                }
            }
        });

        static::updating(function ($lease) {
            if ($lease->isDirty('status')) {
                $lease->previous_status = $lease->getOriginal('status');
            }
        });
    }

    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class);
    }

    public function units()
    {
        return $this->belongsToMany(Property::class, 'lease_units', 'lease_id', 'unit_id')
            ->withPivot('id', 'lease_id', 'unit_id','ejar_id')->withTimestamps();
    }

    public function viewUnits()
    {
        return $this->belongsToMany(Property::class, 'lease_units', 'lease_id', 'unit_id')
            ->with('attributes')
            ->withPivot('id', 'lease_id', 'unit_id')->withTimestamps();
    }

    public function leaseUnits()
    {
        return $this->hasMany(LeaseUnit::class, 'lease_id');
    }

    public function leaseServices()
    {
        return $this->hasMany(LeaseService::class);
    }

    public function members()
    {
        return $this->hasMany(LeaseMember::class, 'lease_id')
            ->where(function ($query) {
                $query->where('member_role', LeaseMemberTypesEnum::LESSOR)
                    ->orWhere('member_role', LeaseMemberTypesEnum::LESSOR_REPRESENTER);
            });
    }

    public function owners()
    {
        return $this->hasMany(LeaseMember::class, 'lease_id')
            ->where(function ($query) {
                $query->where('member_role', LeaseMemberTypesEnum::LESSOR);
            });
    }

    public function ViewMembers()
    {
        return $this->hasMany(LeaseMember::class, 'lease_id')
            ->with(['member','bankAccount'])
            ->where(function ($query) {
                $query->where('member_role', LeaseMemberTypesEnum::LESSOR)
                    ->orWhere('member_role', LeaseMemberTypesEnum::LESSOR_REPRESENTER);
            });
    }

    public function allMembers()
    {
        return $this->hasMany(LeaseMember::class, 'lease_id');
    }

    public function tenant()
    {
        return $this->hasOne(LeaseMember::class, 'lease_id')->where('member_role', LeaseMemberTypesEnum::TENANT);
    }

    public function commission()
    {
        return $this->hasOne(LeaseCommission::class, 'lease_id');
    }

    public function tenantRepresenter()
    {
        return $this->hasOne(LeaseMember::class, 'lease_id')->where('member_role', LeaseMemberTypesEnum::TENANT_REPRESENTER);
    }

    public function lessorRepresenter()
    {
        return $this->hasOne(LeaseMember::class, 'lease_id')->where('member_role', LeaseMemberTypesEnum::LESSOR_REPRESENTER);
    }

    public function document()
    {
        return $this->morphOne(Document::class, 'morphable');
    }

    public function services()
    {
        return $this->hasMany(LeaseUnitService::class, 'lease_id');
    }

    public function leaseMembers()
    {
        return $this->hasMany(LeaseMember::class, 'lease_id');
    }

    public function request()
    {
        return $this->hasOne(Request::class, 'requestable_id')
            ->where('requestable_type', self::class)
            ->whereIn('status', ['pending']);
    }

    public function approvedRequest()
    {
        return $this->hasOne(Request::class, 'requestable_id')
            ->where('requestable_type', self::class)
            ->whereIn('status', ['approved']);
    }

    public function documents()
    {
        return $this->morphMany(Document::class, 'morphable');
    }

    public function invoiceSchedules()
    {
        return $this->hasMany(\Modules\Invoice\app\Models\InvoiceSchedule::class, 'lease_id');
    }

    public function broker() {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function invoices()
    {
        return $this->hasManyThrough(
            Invoice::class,
            InvoiceItem::class,
            'item_id',
            'id',
            'id',
            'invoice_id'
        )->where('item_type', static::class);
    }

    public function terms()
    {
        return $this->hasMany(LeaseTerm::class);
    }

    public function commercial_meta_data()
    {
        return $this->hasOne(CommercialLeaseMeta::class, 'lease_id', 'id');
    }
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function bankAccount(): BelongsTo
    {
        return $this->belongsTo(BankAccount::class, 'bank_account_id');
    }

    public function leaseTerms(): HasMany
    {
        return $this->hasMany(LeaseTerm::class);
    }
// In Lease model
    // In Lease model
    public function unitWithType($unitId)
    {
        return $this->units()
            ->with('propertyType')
            ->where('properties.id', $unitId)
            ->first();
    }

// In Lease model
    public function leaseProperties()
    {
        return $this->belongsToMany(Property::class, 'lease_units', 'lease_id', 'unit_id')
            ->with(['usability', 'amenities'])
            ->withPivot('id', 'lease_id', 'unit_id')
            ->withTimestamps();
    }

    // In Lease model
    public function getPropertyDetails($unitId)
    {
        return $this->units()
            ->with(['usability', 'amenities', 'propertyType', 'attributes'])
            ->where('properties.id', $unitId)
            ->first();
    }

    public function syncSteps()
    {
        return $this->hasMany(SyncEjarLeaseStep::class);
    }
    public function leaseRenewals()
    {
        return $this->hasMany(Lease::class, 'parent_id')->where('sub_lease_type', SubLeaseTypeEnum::RENEWED);
    }
}
