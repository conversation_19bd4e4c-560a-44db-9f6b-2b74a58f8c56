<?php

use Illuminate\Support\Facades\Route;
use Modules\Lease\app\Http\Controllers\Api\LeaseController;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::get("leases/payment-schedule", [LeaseController::class, 'testPaymentSchedule']);//todo remove it after testing
Route::get("leases/lease-balance", [LeaseController::class, 'testLeaseBalance']);//todo remove it after testing
Route::get("leases/paid-is-valid", [LeaseController::class, 'testIsValidPaidAmount']);//todo remove it after testing
Route::get("leases/start-schedule", [LeaseController::class, 'testStartScheduleDate']);//todo remove it after testing
Route::post("leases/delete-lease/{lease}", [LeaseController::class, 'deleteLease']);//todo remove it after testing
Route::middleware(['auth:sanctum', \Modules\Account\app\Middleware\IsCustomerActive::class])->group(function () {
    Route::get("leases/unit-services", [LeaseController::class, 'leaseUnitServices']);
    Route::apiResource("leases", LeaseController::class);
    Route::get("getLeaseBills/{id}", [LeaseController::class, 'getLeaseBills']);
    Route::get("getLeaseFutureSchedule/{id}", [LeaseController::class, 'getLeaseFutureSchedule']);
    Route::get("getLeasePastSchedule/{id}", [LeaseController::class, 'getLeasePastSchedule']);
    Route::get("getLeaseSchedule/{id}", [LeaseController::class, 'getLeaseSchedule']);
    Route::get("getOwnerSchedule/", [LeaseController::class, 'getOwnerSchedule']);
});
