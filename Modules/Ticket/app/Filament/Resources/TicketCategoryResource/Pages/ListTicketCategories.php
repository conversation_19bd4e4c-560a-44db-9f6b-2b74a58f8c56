<?php

namespace Modules\Ticket\app\Filament\Resources\TicketCategoryResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\Ticket\app\Filament\Resources\TicketCategoryResource;

class ListTicketCategories extends ListRecords
{
    protected static string $resource = TicketCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label(__('Add Ticket Category')),
        ];
    }
}
