<?php

namespace Modules\Ticket\Services;

use Illuminate\Support\Str;
use Khaleds\Notifications\Models\NotificationsTemplate;
use Khaleds\Notifications\Services\SendNotification;
use Khaleds\Shared\Services\ServiceAbstract;
use Modules\Account\app\Models\Account;
use Modules\Account\Helpers\AccountHelper;
use Modules\Ticket\Repositories\TicketRepository;
use App\Models\User;

class TicketService extends ServiceAbstract
{

    public function __construct(TicketRepository $repository)
    {
        parent::__construct($repository);
    }

    public function getUserNamesAndIds(): array
    {
        return User::select('name', 'id')->pluck('name', 'id')->toArray();
    }

    public function create(array $data)
    {
        $data['uuid'] = Str::random(10);
        $ticket = parent::create($data);
        //notify admins
        $admins = AccountHelper::AdminUsers();
        $template = NotificationsTemplate::where(['key' => 'new_ticket'])->first();
        if ($template) {
            foreach ($admins as $admin) {
                SendNotification::make(['fcm-web', 'email'])
                    ->template($template->key)
                    ->model(User::class)
                    ->id($admin->id)
                    ->findBody(['{account}'])
                    ->replaceBody([auth()->user()->name])
                    ->icon($template->icon)
                    ->url(url($template->url))
                    ->privacy('private')
                    ->database(true)
                    ->fire();
            }
        }
        return $ticket;
    }

    public function notifyAssignedUser($ticket): void
    {
        $template = NotificationsTemplate::where(['key' => 'ticket_support_assignee'])->first();
        if ($template) {
            SendNotification::make(['fcm-web', 'email'])
                ->template($template->key)
                ->model(User::class)
                ->id($ticket->user_id)
                ->findTitle(['{uuid}'])
                ->replaceTitle([$ticket->uuid])
                ->findBody(['{priority}'])
                ->replaceBody([$ticket->priority])
                ->icon($template->icon)
                ->url(url($template->url))
                ->privacy('private')
                ->database(true)
                ->fire();
        }
    }
    public function notifyAfterCloseTicket($ticket): void
    {
        $template = NotificationsTemplate::where(['key' => 'ticket_closed'])->first();
        if ($template) {
            SendNotification::make(['fcm-api'])
                ->template($template->key)
                ->model(Account::class)
                ->id($ticket->account_id)
                ->findTitle(['{uuid}'])
                ->replaceTitle([$ticket->uuid])
                ->findBody(['{uuid}'])
                ->replaceBody([$ticket->uuid])
                ->icon($template->icon)
                ->url(url($template->url))
                ->privacy('private')
                ->database(true)
                ->fire();
        }
    }
}


