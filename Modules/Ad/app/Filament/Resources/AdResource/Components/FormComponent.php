<?php

namespace Modules\Ad\app\Filament\Resources\AdResource\Components;

use App\Models\Val;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Modules\Ad\app\Models\Ad;
use Modules\Property\app\Models\Property;

class FormComponent
{

    public static function getForm() :array{

        return [

            Group::make([
                Section::make(__('Title & Description'))
                    ->schema([
                       Tabs::make('Tabs')
                            ->tabs([
                                Tabs\Tab::make(__('Arabic'))
                                    ->schema([
                                        TextInput::make('title.ar')
                                            ->label('Title')
                                            ->translateLabel('ar')
                                            ->required(),
                                        Textarea::make('description.ar')
                                            ->label('Description')
                                            ->translateLabel('ar'),
                                    ]),
                                Tabs\Tab::make(__('English'))
                                    ->schema([
                                        TextInput::make('title.en')
                                            ->label('Title')
                                            ->translateLabel('en')
                                            ->required(),
                                        Textarea::make('description.en')
                                            ->label('Description')
                                            ->translateLabel('en'),

                                    ]),
                            ]),
                    ])->columnSpan(1),

            Section::make(__('Link'))
                ->schema([
                    Group::make([
                        Toggle::make('is_property')
                            ->label(__('Property'))
                            ->live()
                            ->reactive()
                            ->default(false)
                            ->afterStateHydrated(function (Set $set, Get $get) {
                                if ($get('property_id')) {
                                    $set('is_property', true);
                                }
                            }),

                        Select::make('property_id')
                            ->label(__('Property Name'))
                            ->searchable()
                            ->options(function () {
                                return Property::whereNotNull('name')
                                    ->get()
                                    ->mapWithKeys(fn ($property) => [
                                        $property->id => $property->name ?? "Property #{$property->id}",
                                    ]);
                            })
                            ->required(fn (Get $get) => $get('is_property')) // ✅ required when is_property is true
                            ->hidden(fn (Get $get) => !$get('is_property'))
                            ->grow(false),

                        TextInput::make('link')
                            ->label(__('Link'))
                            ->maxLength(255)
                            ->required(fn (Get $get) => !$get('is_property')) // ✅ required when is_property is false
                            ->hidden(fn (Get $get) => $get('is_property'))
                            ->grow(false),
                    ]),
                ])
                ->columns(1),
            ]),

            Section::make(__('Image'))
                ->schema([
                    SpatieMediaLibraryFileUpload::make('image')
                        ->label(__('Image'))
                        ->maxSize(5120)
                        ->image()
                        ->imageEditor()
                        ->collection('ads')
                        ->imageEditorAspectRatios([
                            '16:9',
                            '4:3',
                            '1:1',
                        ]),

                    Select::make('val_id')  // Changed from 'val' to 'val_id'
                    ->label(__('Attach Existing Val'))
                        ->options(function ($record) {
                            // Only show options in edit mode and when the val is already attached
                            if ($record) {
                                return Val::query()
                                    ->where('active', true)
                                    ->where('morphable_type', Ad::class)
                                    ->where('morphable_id', $record->id)
                                    ->whereDate('end_date', '>=', now())
                                    ->get()
                                    ->mapWithKeys(function ($val) {
                                        return [$val->id => "#{$val->value} (Valid until: {$val->end_date})"];
                                    });
                            }
                            return [];
                        })
                        ->preload()
                        ->createOptionForm([
                            TextInput::make('value')
                                ->label(__('License number'))
                                ->placeholder(__('License number'))
                                ->required()
                                ->unique(Val::class, 'value'),

                            DatePicker::make('start_date')
                                ->label(__('Start Date'))
                                ->required()
                                ->default(now()),

                            DatePicker::make('end_date')
                                ->label(__('End Date'))
                                ->required()
                                ->after('start_date')
                                ->afterOrEqual(today()),
                        ])
                        ->createOptionUsing(function (array $data) {
                            return Val::create([
                                ...$data,
                                'morphable_type' => Ad::class,
                                'morphable_id' => null,
                                'created_at' => now(),
                                'active' => false,  // Set initially as inactive
                            ])->getKey();
                        })
                        ->searchable()
                        ->searchPrompt(__('Search license numbers'))
                        ->optionsLimit(50)
                        ->loadingMessage(__('Loading...'))
                        ->noSearchResultsMessage(__('No license numbers found'))
                        ->placeholder(__('Select a license number'))
                        ->getOptionLabelUsing(fn ($value): ?string => Val::find($value)?->value)
                        ->dehydrated(true)
                        ->distinct()
                        ->afterStateHydrated(function ($component, $state, $record) {
                            if ($record) {
                                $valId = $record->vals()
                                    ->where('morphable_type', Ad::class)
                                    ->whereDate('end_date', '>=', now())
                                    ->where('active', true)
                                    ->first()?->id;
                                $component->state($valId);
                            }
                        }),
                ])
                ->columnSpan(1),
            Toggle::make('is_active')
                ->label(__('Is Active'))
                ->default(true),
        ];

    }
}
