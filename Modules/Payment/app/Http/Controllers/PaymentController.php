<?php

namespace Modules\Payment\app\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    public function verify(Request $request)
    {
        $payment = new \Modules\Payment\Factories\PaymentFactory();
        $payment = $payment->get('ClickPay');
        $payment = $payment->verify($request);
         
        switch($payment['success'])
        {
            case true:
                return redirect()->route('payment.success' , 'success');
            case false:
                return redirect()->route('payment.success' , 'fail');       
        }
    }

    public function success($status)
    {
        // Return a JSON response for success
        return response()->json([
            'success' => true,
            'message' => 'Payment was successful!',
        ]);
    }
}
