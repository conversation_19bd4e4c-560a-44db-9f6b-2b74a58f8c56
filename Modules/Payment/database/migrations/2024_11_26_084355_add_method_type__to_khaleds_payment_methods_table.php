<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Payment\Enums\PaymentMethodsEnum;
use Modules\Payment\Enums\PaymentMethodTypeEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('khaleds_payment_methods', function (Blueprint $table) {
            $table->enum('method_type', PaymentMethodTypeEnum::getValues())
                ->after('method')
                ->default(PaymentMethodTypeEnum::OFFLINE);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('khaleds_payment_methods', function (Blueprint $table) {
            Schema::table('khaleds_payment_methods', function (Blueprint $table) {
                $table->dropColumn('method_type');
            });
        });
    }
};
