<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('property_owners', function (Blueprint $table) {
            $table->dropColumn(['org_ejar_id', 'registration_date']);
        });
    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
