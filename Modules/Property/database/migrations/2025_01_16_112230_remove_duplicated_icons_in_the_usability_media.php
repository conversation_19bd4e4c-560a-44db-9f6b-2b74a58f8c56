<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $duplicates = DB::table('media')
        ->where('collection_name', 'usability')
        ->select('model_id', DB::raw('MAX(created_at) as latest_created_at'))
        ->groupBy('model_id')
        ->get();

        foreach ($duplicates as $duplicate) {
            $media = DB::table('media')
                ->where('collection_name', 'usability')
                ->where('model_id', $duplicate->model_id)
                ->orderBy('created_at', 'desc')
                ->get();
    
            $media->skip(1)->each(function ($item) {
                DB::table('media')->where('id', $item->id)->delete();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('media', function (Blueprint $table) {
            //
        });
    }
};
