{{-- Widget Carousel Component --}}
@props(['widgets', 'visibleWidgets'])

<style>
    /* Carousel Styles */
    .widget-carousel-container {
        position: relative;
        padding: 0 30px; /* Space for navigation arrows */
    }

    .widget-carousel-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .widget-carousel-nav:hover {
        background: #f9fafb;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .widget-carousel-nav.prev {
        left: -20px;
    }

    .widget-carousel-nav.next {
        right: -20px;
    }

    .dark .widget-carousel-nav {
        background: #374151;
        border-color: #4b5563;
        color: #d1d5db;
    }

    .dark .widget-carousel-nav:hover {
        background: #4b5563;
    }

    /* Ensure carousel items maintain proper spacing */
    .widget-carousel-track {
        display: flex;
        transition: transform 0.3s ease-in-out;
    }

    .widget-carousel-item {
        flex: none;
        padding: 0 8px; /* Half of gap-4 */
    }
</style>

{{-- Carousel for more than 5 widgets --}}
<div class="relative w-full" x-data="widgetCarousel({{ $visibleWidgets }})">
    {{-- Navigation Arrows - Always visible --}}
    <button
        @click="prev()"
        x-show="canGoPrev || canGoNext"
        class="absolute left-0 top-1/2 -translate-y-1/2 z-20 bg-white dark:bg-gray-800 shadow-lg rounded-full p-1.5 sm:p-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-200 dark:border-gray-600"
        style="margin-left: -8px;"
        :class="{ 'opacity-50 cursor-not-allowed': isRTL ? !canGoNext : !canGoPrev }"
    >
        <svg class="w-4 h-4 sm:w-5 sm:h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
    </button>

    <button
        @click="next()"
        x-show="canGoPrev || canGoNext"
        class="absolute right-0 top-1/2 -translate-y-1/2 z-20 bg-white dark:bg-gray-800 shadow-lg rounded-full p-1.5 sm:p-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border border-gray-200 dark:border-gray-600"
        style="margin-right: -8px;"
        :class="{ 'opacity-50 cursor-not-allowed': isRTL ? !canGoPrev : !canGoNext }"
    >
        <svg class="w-4 h-4 sm:w-5 sm:h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
    </button>

    {{-- Carousel Container with Touch Support --}}
    <div
        class="overflow-hidden cursor-grab active:cursor-grabbing select-none"
        x-ref="carouselContainer"
        @mousedown="startDrag($event)"
        @touchstart="startDrag($event)"
        @mousemove="drag($event)"
        @touchmove="drag($event)"
        @mouseup="endDrag()"
        @touchend="endDrag()"
        @mouseleave="endDrag()"
        @dragstart.prevent
        @contextmenu.prevent
    >
        <div
            class="flex transition-transform duration-300 ease-in-out gap-4"
            :style="`transform: translateX(${getCurrentTransform()}%)`"
            x-ref="carousel"
        >
            @foreach($widgets as $permission => $class)
                @can($permission)
                    <div class="flex-none w-full sm:w-1/2 md:w-1/3 lg:w-1/3 xl:w-1/4 2xl:w-1/5">
                        @livewire($class)
                    </div>
                @endcan
            @endforeach
        </div>
    </div>

    {{-- Dots Indicator --}}
    <div class="flex justify-center mt-4 space-x-2">
        <template x-for="(dot, index) in totalSlides" :key="index">
            <button
                @click="goToSlide(index)"
                class="w-2 h-2 rounded-full transition-colors"
                :class="currentSlide === index ? 'bg-primary-500' : 'bg-gray-300 dark:bg-gray-600'"
            ></button>
        </template>
    </div>
</div>

{{-- Carousel JavaScript --}}
<script>
    function widgetCarousel(visibleWidgets) {
        return {
            currentSlide: 0,
            totalSlides: 0,
            slideWidth: 0,
            itemsPerSlide: 5, // Default for 2xl screens
            isDragging: false,
            startX: 0,
            currentX: 0,
            dragThreshold: 50, // Minimum distance to trigger slide change
            isRTL: false,
            visibleWidgets: visibleWidgets,

            init() {
                // Detect RTL direction
                this.isRTL = document.documentElement.dir === 'rtl' ||
                            document.body.dir === 'rtl' ||
                            getComputedStyle(document.documentElement).direction === 'rtl';

                // Bind global event handlers to maintain 'this' context
                this.handleGlobalMouseMove = this.handleGlobalMouseMove.bind(this);
                this.handleGlobalMouseUp = this.handleGlobalMouseUp.bind(this);

                this.calculateSlides();
                this.updateItemsPerSlide();

                // Recalculate on window resize
                window.addEventListener('resize', () => {
                    this.updateItemsPerSlide();
                    this.calculateSlides();
                    // Ensure current slide is still valid
                    if (this.currentSlide >= this.totalSlides) {
                        this.currentSlide = Math.max(0, this.totalSlides - 1);
                    }
                });
            },

            updateItemsPerSlide() {
                const width = window.innerWidth;
                if (width >= 1536) { // 2xl
                    this.itemsPerSlide = 5;
                } else if (width >= 1280) { // xl
                    this.itemsPerSlide = 4;
                } else if (width >= 1024) { // lg
                    this.itemsPerSlide = 3;
                } else if (width >= 768) { // md
                    this.itemsPerSlide = 3;
                } else if (width >= 640) { // sm
                    this.itemsPerSlide = 2;
                } else {
                    this.itemsPerSlide = 1;
                }
            },

            calculateSlides() {
                const totalItems = this.visibleWidgets;
                this.totalSlides = Math.max(1, Math.ceil(totalItems / this.itemsPerSlide));
                this.slideWidth = 100 / this.itemsPerSlide;
            },

            next() {
                if (this.isRTL) {
                    // In RTL, "next" button should go to previous slide (right to left reading)
                    if (this.canGoPrev) {
                        this.currentSlide--;
                    }
                } else {
                    // In LTR, "next" button goes to next slide
                    if (this.canGoNext) {
                        this.currentSlide++;
                    }
                }
            },

            prev() {
                if (this.isRTL) {
                    // In RTL, "prev" button should go to next slide (right to left reading)
                    if (this.canGoNext) {
                        this.currentSlide++;
                    }
                } else {
                    // In LTR, "prev" button goes to previous slide
                    if (this.canGoPrev) {
                        this.currentSlide--;
                    }
                }
            },

            goToSlide(index) {
                this.currentSlide = index;
            },

            // Touch/Mouse drag functionality
            startDrag(event) {
                this.isDragging = true;
                this.startX = this.getEventX(event);
                this.currentX = this.startX;

                // Disable transitions during drag
                this.$refs.carousel.style.transition = 'none';

                // Prevent default behaviors
                event.preventDefault();

                // Add global mouse event listeners for better drag handling
                if (event.type === 'mousedown') {
                    document.addEventListener('mousemove', this.handleGlobalMouseMove);
                    document.addEventListener('mouseup', this.handleGlobalMouseUp);
                }
            },

            drag(event) {
                if (!this.isDragging) return;

                this.currentX = this.getEventX(event);
                let deltaX = this.currentX - this.startX;

                // Apply drag effect with resistance
                const dragPercent = (deltaX / this.$refs.carouselContainer.offsetWidth) * 100;
                const currentTransform = this.getCurrentTransform();

                // For RTL, we need to reverse the drag direction
                const adjustedDragPercent = this.isRTL ? -dragPercent : dragPercent;
                const newTransform = currentTransform + adjustedDragPercent;

                this.$refs.carousel.style.transform = `translateX(${newTransform}%)`;
            },

            endDrag() {
                if (!this.isDragging) return;

                this.isDragging = false;

                // Remove global event listeners
                document.removeEventListener('mousemove', this.handleGlobalMouseMove);
                document.removeEventListener('mouseup', this.handleGlobalMouseUp);

                // Re-enable transitions
                this.$refs.carousel.style.transition = 'transform 0.3s ease-in-out';

                let deltaX = this.currentX - this.startX;

                // Determine if we should change slides based on drag direction
                if (Math.abs(deltaX) > this.dragThreshold) {
                    if (this.isRTL) {
                        // In RTL: drag right (positive deltaX) = next slide, drag left (negative deltaX) = prev slide
                        // This follows the natural RTL reading direction
                        if (deltaX > 0 && this.canGoNext) {
                            this.currentSlide++;
                        } else if (deltaX < 0 && this.canGoPrev) {
                            this.currentSlide--;
                        }
                    } else {
                        // In LTR: drag right (positive deltaX) = prev slide, drag left (negative deltaX) = next slide
                        if (deltaX > 0 && this.canGoPrev) {
                            this.currentSlide--;
                        } else if (deltaX < 0 && this.canGoNext) {
                            this.currentSlide++;
                        }
                    }
                }

                // Always snap to the current slide position
                this.$refs.carousel.style.transform = `translateX(${this.getCurrentTransform()}%)`;
            },

            // Global mouse event handlers for better drag experience
            handleGlobalMouseMove(event) {
                this.drag(event);
            },

            handleGlobalMouseUp(event) {
                this.endDrag();
            },

            getEventX(event) {
                return event.type.includes('mouse') ? event.clientX : event.touches[0].clientX;
            },

            getCurrentTransform() {
                if (this.isRTL) {
                    // In RTL, positive values move content to the right (showing previous slides)
                    return (this.currentSlide * this.slideWidth);
                } else {
                    // In LTR, negative values move content to the left (showing next slides)
                    return -(this.currentSlide * this.slideWidth);
                }
            },

            get canGoNext() {
                return this.currentSlide < this.totalSlides - 1;
            },

            get canGoPrev() {
                return this.currentSlide > 0;
            }
        }
    }
</script>
