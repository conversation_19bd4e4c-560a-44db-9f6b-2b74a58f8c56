{{-- resources/views/filament/resources/role-resource/pages/view-role-users.blade.php --}}

<x-filament-panels::page>
    <div class="space-y-6">
        {{-- Page Header with Role Info --}}
        <div class="bg-white dark:bg-gray-900 rounded-lg shadow p-6">
            <div class="flex items-center justify-between">
                <div>

                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                     <!--   {{ __('Total users: :count', ['count' => $this->getTableQuery()?->count()]) }} -->
                    </p>

                </div>
            </div>

            @if($this->record->permissions_count > 0)
                <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        <span class="font-medium">{{ __('Permissions:') }}</span>
                        {{ $this->record->permissions_count }} {{ __('permissions assigned') }}
                    </p>
                </div>
            @endif
        </div>

        {{-- Users Table --}}
        <div class="bg-white dark:bg-gray-900 rounded-lg shadow">
            {{ $this->table }}
        </div>
    </div>
</x-filament-panels::page>
