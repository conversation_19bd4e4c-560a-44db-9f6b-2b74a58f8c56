<x-filament-panels::page>
    {{-- Custom Styles for Responsive Dashboard --}}
    <style>
        /* Ensure chart widgets have consistent heights on larger screens */
        @media (min-width: 1024px) {
            .dashboard-chart-container {
                display: grid;
                align-items: stretch;
            }

            .dashboard-chart-container .dashboard-widget-container {
                display: flex;
                flex-direction: column;
            }

            .dashboard-chart-container .dashboard-widget-container > div {
                flex: 1;
                display: flex;
                flex-direction: column;
            }

            .dashboard-chart-container .fi-wi-chart {
                flex: 1;
                display: flex;
                flex-direction: column;
            }

            .dashboard-chart-container .fi-section {
                flex: 1;
                display: flex;
                flex-direction: column;
            }

            .dashboard-chart-container .fi-section-content {
                flex: 1;
                display: flex;
                flex-direction: column;
            }
        }

        /* Ensure widgets don't get cropped */
        .dashboard-widget-container {
            min-height: fit-content;
            overflow: visible;
        }

        /* Mobile responsiveness */
        @media (max-width: 767px) {
            .custom_widget_page .grid {
                gap: 1rem;
            }
        }


    </style>

    {{-- Welcome Widget --}}
    <div class="mb-0">
        @livewire(\App\Filament\Resources\WidgetResource\Widgets\WelcomeWidget::class)
    </div>

    <div class="grid grid-cols-12 gap-4 lg:gap-8 custom_widget_page">

        {{-- Widgets Section --}}
        <div class="col-span-12 lg:col-span-12 grid ">
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-4">

                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalLeasesCount::class)

                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalInvoicesCount::class)

                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalPropertiesCount::class)

                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalTicketsCount::class)

            </div>
        </div>

        {{-- Custom Widgets Section --}}
        @php
            // Count visible widgets based on permissions
            $visibleWidgets = 0;
            $widgets = [
                'widget_CompanyCount' => \App\Filament\Resources\WidgetResource\Widgets\CompanyCount::class,
                'widget_ActiveLeaseCount' => \App\Filament\Resources\WidgetResource\Widgets\ActiveLeaseCount::class,
                'widget_InactiveLeaseCount' => \App\Filament\Resources\WidgetResource\Widgets\InactiveLeaseCount::class,
                'widget_EjarRegisteredLeaseCount' => \App\Filament\Resources\WidgetResource\Widgets\EjarRegisteredLeaseCount::class,
                'widget_ActiveMaintenanceRequestsCount' => \App\Filament\Resources\WidgetResource\Widgets\ActiveMaintenanceRequestsCount::class,
                'widget_InactiveMaintenanceRequestsCount' => \App\Filament\Resources\WidgetResource\Widgets\InactiveMaintenanceRequestsCount::class,
                'widget_CommissionInvoicesTotal' => \App\Filament\Resources\WidgetResource\Widgets\CommissionInvoicesTotal::class,
                'widget_TotalPaidPayments' => \App\Filament\Resources\WidgetResource\Widgets\TotalPaidPayments::class,
                'widget_TotalUnpaidPayments' => \App\Filament\Resources\WidgetResource\Widgets\TotalUnpaidPayments::class,
                'widget_InactiveSubscription' => \App\Filament\Resources\WidgetResource\Widgets\InactiveSubscription::class,
                'widget_ActiveSubscription' => \App\Filament\Resources\WidgetResource\Widgets\ActiveSubscription::class,
                'widget_TotalSubscriptionIncome' => \App\Filament\Resources\WidgetResource\Widgets\TotalSubscriptionIncome::class,
                'widget_TotalSubscriptionUpcome' => \App\Filament\Resources\WidgetResource\Widgets\TotalSubscriptionUpcome::class,
                'widget_ProperitiesManagedCount' => \App\Filament\Resources\WidgetResource\Widgets\ProperitiesManagedCount::class,
                'widget_TotalRentCurrentMonth' => \App\Filament\Resources\WidgetResource\Widgets\TotalRentCurrentMonth::class,
            ];

            foreach ($widgets as $permission => $class) {
                if (auth()->user()->can($permission)) {
                    $visibleWidgets++;
                }
            }
        @endphp

        <div class="col-span-12 lg:col-span-12 {{ $visibleWidgets === 0 ? 'hidden' : ($visibleWidgets > 5 ? 'flex' : 'grid') }}">

            @if($visibleWidgets > 5)
                {{-- Use Carousel Component for more than 5 widgets --}}
                <x-widget-carousel :widgets="$widgets" :visibleWidgets="$visibleWidgets" />
            @else
                {{-- Regular Grid for 5 or fewer widgets --}}
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
                    @foreach($widgets as $permission => $class)
                        @can($permission)
                            @livewire($class)
                        @endcan
                    @endforeach
                </div>
            @endif
        </div>

        {{-- Charts Section --}}
        <div class="col-span-12 lg:col-span-12">
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6 dashboard-chart-container">
                {{-- Lease Analytics Chart --}}
                <div class="col-span-1 dashboard-widget-container">
                    <div class="h-full min-h-[300px] lg:min-h-[350px]">
                        @livewire(\App\Filament\Resources\WidgetResource\Widgets\LeaseAnalyticsWidget::class)
                    </div>
                </div>

                {{-- Invoice Analytics Chart --}}
                <div class="col-span-1 dashboard-widget-container">
                    <div class="h-full min-h-[300px] lg:min-h-[350px]">
                        @livewire(\App\Filament\Resources\WidgetResource\Widgets\InvoiceAnalyticsWidget::class)
                    </div>
                </div>

                {{-- Property Analytics Chart --}}
                <div class="col-span-1 md:col-span-2 xl:col-span-1 dashboard-widget-container">
                    <div class="h-full min-h-[300px] lg:min-h-[350px]">
                        @livewire(\App\Filament\Resources\WidgetResource\Widgets\PropertyAnalyticsWidget::class)
                    </div>
                </div>
            </div>
        </div>

        {{-- Table Widgets Section --}}
        <div class="col-span-12">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
                {{-- Latest Leases Widget --}}
                <div class="col-span-1 dashboard-widget-container">
                    <div class="h-full">
                        @livewire(\App\Filament\Resources\WidgetResource\Widgets\LatestLeasesWidget::class)
                    </div>
                </div>

                {{-- Latest Invoices Widget --}}
                <div class="col-span-1 dashboard-widget-container">
                    <div class="h-full">
                        @livewire(\App\Filament\Resources\WidgetResource\Widgets\LatestInvoicesWidget::class)
                    </div>
                </div>
            </div>
        </div>
    </div>


</x-filament-panels::page>
