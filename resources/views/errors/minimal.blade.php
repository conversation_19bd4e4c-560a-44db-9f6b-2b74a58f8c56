@extends('errors::layout')

@section('title', __('Error'))

@section('icon-bg-color', 'bg-gray-100 dark:bg-gray-900/20')

@section('icon')
<svg class="h-8 w-8 text-gray-600 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
</svg>
@endsection

@section('message')
<p class="text-lg text-gray-600 dark:text-gray-300 flex items-center justify-center gap-2">
    <span class="text-gray-500">❗</span>
    {{ $exception->getMessage() ?: __('An error occurred.') }}
</p>
<p class="text-sm text-gray-500 dark:text-gray-400">
    {{ __('Please try again or contact support if the problem persists.') }}
</p>
@endsection

@section('actions')
<a href="{{ route('filament.admin.pages.dashboard') }}"
   class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-[#246250] hover:bg-[#1e5244] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#246250] transition-colors duration-200">
    {{ __('Back to Dashboard') }}
</a>

<a href="{{ route('filament.admin.resources.tickets.index') }}"
   class="w-full flex justify-center py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#246250] transition-colors duration-200">
    {{ __('Open Support Ticket') }}
</a>
@endsection

@section('error-code', __('Error Code: :code', ['code' => $exception->getStatusCode() ?? 'Unknown']))
