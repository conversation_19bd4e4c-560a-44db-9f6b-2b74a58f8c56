@extends('errors::layout')

@section('title', __('Access Forbidden'))

@section('icon-bg-color', 'bg-orange-100 dark:bg-orange-900/20')

@section('icon')
<svg class="h-8 w-8 text-orange-600 dark:text-orange-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
</svg>
@endsection

@section('message')
<p class="text-lg text-gray-600 dark:text-gray-300 flex items-center justify-center gap-2">
    <span class="text-orange-500">🔒</span>
    {{ __('Access to this page is not allowed.') }}
</p>
<p class="text-sm text-gray-500 dark:text-gray-400">
    {{ __('If you believe this is a mistake, please open a support ticket.') }}
</p>
@endsection

@section('actions')
<a href="{{ route('filament.admin.pages.dashboard') }}"
   class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-[#246250] hover:bg-[#1e5244] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#246250] transition-colors duration-200">
    {{ __('Back to Dashboard') }}
</a>

<a href="{{ route('filament.admin.resources.tickets.index') }}"
   class="w-full flex justify-center py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#246250] transition-colors duration-200">
    {{ __('Open Support Ticket') }}
</a>
@endsection

@section('error-code', __('Error Code: 403'))
